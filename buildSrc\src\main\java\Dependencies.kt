package dependencies

object Versions {
    // Android
    val android_gradle_plugin = "8.1.4"
    val support = "1.0.2"
    val lifecycle = "2.7.0"
    val room = "2.6.1"
    val navigation = "2.7.6"
    val work = "2.9.0"
    val app_compat = "1.6.1"
    val material = "1.11.0"
    val recycler_view = "1.3.2"
    val transision = "1.4.1"
    val media = "1.7.0"
    val constraint_layout = "2.1.4"
    val ktx = "1.12.0"

    // Firebase Crashlytics (replaces Fabric)
    val firebase_crashlytics_plugin = "2.9.9"
    val firebase_crashlytics = "18.6.1"

    // Google
    val firebase = "32.7.0"
    val firebase_messageing = "23.4.0"
    val firebase_ml = "24.1.0" // ML Kit replaces Firebase ML Vision
    val google_service_plugin = "4.4.0"

    // Kotlin
    val kotlin = "1.9.20"
    val kotlin_coroutine = "1.7.3"

    // Test
    val junit = "4.13.2"
    val test_runner = "1.5.2"
    val espresso = "3.5.1"
    val mockito = "5.8.0"

    // 3rd party
    val glide = "4.16.0"
    val subsampling_image_view = "3.10.0"
    val lottie = "6.2.0"
    val better_link_movement_method = "2.2.0"

    // Mozilla
    val android_components = "124.0.0"

    // Adjust
    val adjust = "4.38.0"
    val android_install_referrer = "2.1"

    // License
    val license_plugin = "0.10.6"
    val license = "17.0.1"
}

object Deps {
    object support {
        val app_compat = "androidx.appcompat:appcompat:${Versions.app_compat}"
        val material = "com.google.android.material:material:${Versions.material}"
        val card_view = "androidx.cardview:cardview:${Versions.support}"
        val recycler_view = "androidx.recyclerview:recyclerview:${Versions.recycler_view}"
        val transition = "androidx.transition:transition:${Versions.transision}"
        val preference = "androidx.legacy:legacy-preference-v14:${Versions.support}"
        val media = "androidx.media:media:${Versions.media}"
        val v4 = "androidx.legacy:legacy-support-v4:${Versions.support}"
        val constraint_layout = "androidx.constraintlayout:constraintlayout:${Versions.constraint_layout}"
    }

    object navigation {
        val ui = "androidx.navigation:navigation-ui:${Versions.navigation}"
        val fragment = "androidx.navigation:navigation-fragment:${Versions.navigation}"
    }

    object work {
        val runtime = "androidx.work:work-runtime:${Versions.work}"
    }

    object firebase {
        val bom = "com.google.firebase:firebase-bom:${Versions.firebase}"
        val analytics = "com.google.firebase:firebase-analytics"
        val messaging = "com.google.firebase:firebase-messaging"
        val crashlytics = "com.google.firebase:firebase-crashlytics:${Versions.firebase_crashlytics}"
        val ml_text = "com.google.mlkit:text-recognition:${Versions.firebase_ml}" // ML Kit replaces Firebase ML Vision
        val play_services_analytics = "com.google.android.gms:play-services-analytics:17.0.1" // Required by Adjust
    }

    object room {
        val runtime = "androidx.room:room-runtime:${Versions.room}"
        val compiler = "androidx.room:room-compiler:${Versions.room}"
    }

    object lifecycle {
        val common = "androidx.lifecycle:lifecycle-common-java8:${Versions.lifecycle}"
        val viewmodel_ktx = "androidx.lifecycle:lifecycle-viewmodel-ktx:${Versions.lifecycle}"
        val livedata_ktx = "androidx.lifecycle:lifecycle-livedata-ktx:${Versions.lifecycle}"
    }

    object crashlytics {
        val plugin = "com.google.firebase:firebase-crashlytics-gradle:${Versions.firebase_crashlytics_plugin}"
    }

    object mozilla {
        val search = "org.mozilla.components:browser-search:${Versions.android_components}"
        val telemetry = "org.mozilla.components:service-telemetry:${Versions.android_components}"
    }

    object ktx {
        val core = "androidx.core:core-ktx:${Versions.ktx}"
    }

    object kotlin {
        val stdlib = "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${Versions.kotlin}"
        val coroutine = "org.jetbrains.kotlinx:kotlinx-coroutines-android:${Versions.kotlin_coroutine}"
        val plugin = "org.jetbrains.kotlin:kotlin-gradle-plugin:${Versions.kotlin}"
    }

    object espresso {
        val core = "androidx.test.espresso:espresso-core:${Versions.espresso}"
    }

    object support_test {
        val runner = "androidx.test:runner:${Versions.test_runner}"
    }

    object mockito {
        val core = "org.mockito:mockito-core:${Versions.mockito}"
    }

    object gms {
        val plugin = "com.google.gms:google-services:${Versions.google_service_plugin}" // google-services plugin
    }

    val junit = "junit:junit:${Versions.junit}"
    val glide = "com.github.bumptech.glide:glide:${Versions.glide}"
    val adjust = "com.adjust.sdk:adjust-android:${Versions.adjust}"
    val install_referrer = "com.android.installreferrer:installreferrer:${Versions.android_install_referrer}"
}
